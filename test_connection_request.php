<?php
/**
 * Test Connection Request Functionality
 */

require_once 'config.php';
require_once 'includes/db_connect.php';
require_once 'includes/social_functions.php';

session_start();

echo "<h1>Connection Request Test</h1>";

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "<p style='color: red;'>❌ No user logged in. Please log in first.</p>";
    exit;
}

$currentUserId = $_SESSION['user_id'];
echo "<p><strong>Current User ID:</strong> $currentUserId</p>";

// Get current user info
$stmt = $conn->prepare("SELECT username, email FROM users WHERE id = ?");
$stmt->bind_param("i", $currentUserId);
$stmt->execute();
$user = $stmt->get_result()->fetch_assoc();
echo "<p><strong>Current User:</strong> " . $user['username'] . " (" . $user['email'] . ")</p>";

// Check if user_connections table exists
echo "<h2>1. Database Check</h2>";
$tableCheck = $conn->query("SHOW TABLES LIKE 'user_connections'");
if ($tableCheck->num_rows > 0) {
    echo "<p style='color: green;'>✅ user_connections table exists</p>";
} else {
    echo "<p style='color: red;'>❌ user_connections table missing</p>";
    exit;
}

// Show current connection requests
echo "<h2>2. Current Connection Requests</h2>";
$result = $conn->query("
    SELECT uc.*, 
           u1.username as requester_name, 
           u2.username as receiver_name
    FROM user_connections uc
    JOIN users u1 ON uc.requester_id = u1.id
    JOIN users u2 ON uc.receiver_id = u2.id
    ORDER BY uc.created_at DESC
    LIMIT 10
");

if ($result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Requester</th><th>Receiver</th><th>Status</th><th>Date</th></tr>";
    while ($row = $result->fetch_assoc()) {
        $style = '';
        if ($row['requester_id'] == $currentUserId || $row['receiver_id'] == $currentUserId) {
            $style = 'background-color: #ffffcc;'; // Highlight current user's requests
        }
        echo "<tr style='$style'>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['requester_name'] . " (ID: " . $row['requester_id'] . ")</td>";
        echo "<td>" . $row['receiver_name'] . " (ID: " . $row['receiver_id'] . ")</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "<td>" . $row['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No connection requests found.</p>";
}

// Show users available to connect with
echo "<h2>3. Test Connection Requests</h2>";
$result = $conn->query("
    SELECT u.id, u.username 
    FROM users u 
    WHERE u.id != $currentUserId 
    AND u.id NOT IN (
        SELECT CASE 
            WHEN requester_id = $currentUserId THEN receiver_id 
            ELSE requester_id 
        END 
        FROM user_connections 
        WHERE requester_id = $currentUserId OR receiver_id = $currentUserId
    )
    LIMIT 5
");

if ($result->num_rows > 0) {
    echo "<p>Click these buttons to test sending connection requests:</p>";
    while ($row = $result->fetch_assoc()) {
        echo "<button onclick=\"testConnectionRequest(" . $row['id'] . ", this)\">";
        echo "Send Request to " . $row['username'] . " (ID: " . $row['id'] . ")";
        echo "</button> ";
    }
} else {
    echo "<p>No users available to send connection requests to.</p>";
}

// Manual test section
echo "<h3>Manual Test</h3>";
echo "<input type='number' id='testUserId' placeholder='User ID to connect with'>";
echo "<button onclick='manualConnectionTest()'>Send Connection Request</button>";

// Show recent errors
echo "<h2>4. Recent PHP Errors</h2>";
$errorLogFile = __DIR__ . '/php_errors.log';
if (file_exists($errorLogFile)) {
    $lines = file($errorLogFile);
    $recentLines = array_slice($lines, -10);
    echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 200px; overflow-y: scroll;'>";
    echo htmlspecialchars(implode('', $recentLines));
    echo "</pre>";
} else {
    echo "<p>No error log file found.</p>";
}

?>

<script>
function testConnectionRequest(userId, button) {
    console.log('Testing connection request to user:', userId);
    
    button.disabled = true;
    button.textContent = 'Processing...';
    
    fetch('<?php echo APP_URL; ?>/api/social_actions.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=connect&user_id=${userId}`
    })
    .then(response => response.json())
    .then(data => {
        console.log('Connection request response:', data);
        alert('Connection Request Response:\n' + JSON.stringify(data, null, 2));
        if (data.success) {
            location.reload();
        } else {
            button.disabled = false;
            button.textContent = button.textContent.replace('Processing...', 'Send Request to');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error: ' + error.message);
        button.disabled = false;
        button.textContent = button.textContent.replace('Processing...', 'Send Request to');
    });
}

function manualConnectionTest() {
    const userId = document.getElementById('testUserId').value;
    if (!userId) {
        alert('Please enter a user ID');
        return;
    }
    
    console.log('Manual connection request test for user:', userId);
    
    fetch('<?php echo APP_URL; ?>/api/social_actions.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=connect&user_id=${userId}`
    })
    .then(response => response.json())
    .then(data => {
        console.log('Manual connection request response:', data);
        alert('Manual Connection Request Response:\n' + JSON.stringify(data, null, 2));
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error: ' + error.message);
    });
}
</script>

<style>
table {
    margin: 10px 0;
}
th, td {
    padding: 8px;
    text-align: left;
}
th {
    background: #f8f9fa;
}
button {
    margin: 5px;
    padding: 5px 10px;
    cursor: pointer;
}
input {
    margin: 5px;
    padding: 5px;
}
</style>
