<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create MoodifyMe Favicon</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .favicon-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .favicon-preview {
            display: inline-block;
            margin: 20px;
            text-align: center;
        }
        .favicon-canvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .download-btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .download-btn:hover {
            background: #0056b3;
            color: white;
            text-decoration: none;
        }
        .size-label {
            font-weight: bold;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="favicon-container">
        <h1>🎭 MoodifyMe Favicon Generator</h1>
        <p>Creating modern, mood-themed favicons for your application</p>
        
        <div class="favicon-preview">
            <canvas id="favicon16" class="favicon-canvas" width="16" height="16"></canvas>
            <div class="size-label">16x16 (ICO)</div>
            <a href="#" id="download16" class="download-btn">Download 16x16</a>
        </div>
        
        <div class="favicon-preview">
            <canvas id="favicon32" class="favicon-canvas" width="32" height="32"></canvas>
            <div class="size-label">32x32 (PNG)</div>
            <a href="#" id="download32" class="download-btn">Download 32x32</a>
        </div>
        
        <div class="favicon-preview">
            <canvas id="favicon64" class="favicon-canvas" width="64" height="64"></canvas>
            <div class="size-label">64x64 (PNG)</div>
            <a href="#" id="download64" class="download-btn">Download 64x64</a>
        </div>
        
        <div class="favicon-preview">
            <canvas id="favicon128" class="favicon-canvas" width="128" height="128"></canvas>
            <div class="size-label">128x128 (PNG)</div>
            <a href="#" id="download128" class="download-btn">Download 128x128</a>
        </div>
        
        <p><strong>Instructions:</strong></p>
        <ol>
            <li>Download the 32x32 PNG and save as <code>favicon.ico</code> in <code>assets/images/</code></li>
            <li>Download other sizes for better browser support</li>
            <li>The design features a mood emoji with gradient background</li>
        </ol>
    </div>

    <script>
        function createFavicon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // Scale factor for drawing
            const scale = size / 32;
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(0.5, '#764ba2');
            gradient.addColorStop(1, '#f093fb');
            
            // Draw background circle
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 1, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw mood emoji (happy face)
            ctx.fillStyle = '#ffffff';
            ctx.font = `bold ${Math.floor(size * 0.6)}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // Use a mood-related emoji
            const emoji = size >= 32 ? '😊' : '☺';
            ctx.fillText(emoji, size/2, size/2);
            
            // Add subtle border
            ctx.strokeStyle = 'rgba(255,255,255,0.3)';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 1, 0, 2 * Math.PI);
            ctx.stroke();
        }
        
        function setupDownload(canvasId, downloadId, filename) {
            const canvas = document.getElementById(canvasId);
            const downloadLink = document.getElementById(downloadId);
            
            downloadLink.addEventListener('click', function(e) {
                e.preventDefault();
                const link = document.createElement('a');
                link.download = filename;
                link.href = canvas.toDataURL('image/png');
                link.click();
            });
        }
        
        // Create favicons
        createFavicon(document.getElementById('favicon16'), 16);
        createFavicon(document.getElementById('favicon32'), 32);
        createFavicon(document.getElementById('favicon64'), 64);
        createFavicon(document.getElementById('favicon128'), 128);
        
        // Setup downloads
        setupDownload('favicon16', 'download16', 'favicon-16x16.png');
        setupDownload('favicon32', 'download32', 'favicon-32x32.png');
        setupDownload('favicon64', 'download64', 'favicon-64x64.png');
        setupDownload('favicon128', 'download128', 'favicon-128x128.png');
        
        console.log('MoodifyMe Favicon Generator Ready!');
        console.log('Download the 32x32 version and rename it to favicon.ico');
    </script>
</body>
</html>
