<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Voice Message API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .error { color: red; background: #ffe6e6; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .success { color: green; background: #e6ffe6; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .debug { background: #f0f0f0; padding: 10px; border-radius: 5px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        button { padding: 10px 20px; margin: 5px; }
        #recordButton { background: #ff4444; color: white; border: none; border-radius: 5px; }
        #recordButton.recording { background: #44ff44; }
        #sendButton { background: #4444ff; color: white; border: none; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Voice Message API Test</h1>
    
    <div>
        <h3>Test Voice Recording & Sending</h3>
        <p>Conversation ID: <input type="number" id="conversationId" value="<?php echo isset($_GET['conversation_id']) ? (int)$_GET['conversation_id'] : 1; ?>" min="1"></p>
        <button id="recordButton" onclick="toggleRecording()">🎤 Start Recording</button>
        <button id="sendButton" onclick="sendVoiceMessage()" disabled>📤 Send Voice Message</button>
        <button id="testApiButton" onclick="testApiDirectly()">🧪 Test API Directly</button>
        <p>Duration: <span id="duration">0</span> seconds</p>
        <p><a href="create_test_conversation.php">Create Test Conversation</a> | <a href="pages/messages.php">Go to Messages</a></p>
    </div>
    
    <div id="results"></div>

    <script>
        let mediaRecorder;
        let audioChunks = [];
        let recordingStartTime;
        let isRecording = false;
        let recordedBlob = null;

        async function toggleRecording() {
            const button = document.getElementById('recordButton');
            const sendButton = document.getElementById('sendButton');
            
            if (!isRecording) {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    mediaRecorder = new MediaRecorder(stream);
                    audioChunks = [];
                    
                    mediaRecorder.ondataavailable = event => {
                        audioChunks.push(event.data);
                    };
                    
                    mediaRecorder.onstop = () => {
                        recordedBlob = new Blob(audioChunks, { type: 'audio/webm' });
                        sendButton.disabled = false;
                        
                        // Stop all tracks
                        stream.getTracks().forEach(track => track.stop());
                    };
                    
                    mediaRecorder.start();
                    recordingStartTime = Date.now();
                    isRecording = true;
                    
                    button.textContent = '⏹️ Stop Recording';
                    button.classList.add('recording');
                    
                    // Update duration
                    const updateDuration = () => {
                        if (isRecording) {
                            const duration = Math.round((Date.now() - recordingStartTime) / 1000);
                            document.getElementById('duration').textContent = duration;
                            setTimeout(updateDuration, 100);
                        }
                    };
                    updateDuration();
                    
                } catch (error) {
                    showResult('Error accessing microphone: ' + error.message, 'error');
                }
            } else {
                mediaRecorder.stop();
                isRecording = false;
                
                button.textContent = '🎤 Start Recording';
                button.classList.remove('recording');
            }
        }

        async function sendVoiceMessage() {
            if (!recordedBlob) {
                showResult('No recording available', 'error');
                return;
            }
            
            const conversationId = document.getElementById('conversationId').value;
            const duration = Math.round((Date.now() - recordingStartTime) / 1000);
            
            const formData = new FormData();
            formData.append('voice_file', recordedBlob, 'voice_message.webm');
            formData.append('conversation_id', conversationId);
            formData.append('duration', duration);
            
            showResult('Sending voice message...', 'debug');
            
            try {
                const response = await fetch('api/send_voice_message.php', {
                    method: 'POST',
                    body: formData
                });
                
                const responseText = await response.text();
                showResult('Raw Response:\n' + responseText, 'debug');
                
                try {
                    const result = JSON.parse(responseText);
                    
                    if (result.success) {
                        showResult('✅ Voice message sent successfully!', 'success');
                        if (result.debug) {
                            showResult('Debug Info:\n' + JSON.stringify(result.debug, null, 2), 'debug');
                        }
                    } else {
                        showResult('❌ Failed to send voice message: ' + result.message, 'error');
                        if (result.debug) {
                            showResult('Debug Info:\n' + JSON.stringify(result.debug, null, 2), 'debug');
                        }
                        if (result.error) {
                            showResult('Error Details: ' + result.error, 'error');
                        }
                    }
                } catch (parseError) {
                    showResult('❌ Failed to parse JSON response: ' + parseError.message, 'error');
                }
                
            } catch (error) {
                showResult('❌ Network error: ' + error.message, 'error');
            }
        }

        function showResult(message, type) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = message; // Changed to innerHTML to support links
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        // Test API directly with a dummy file
        async function testApiDirectly() {
            const conversationId = document.getElementById('conversationId').value;

            // Create a dummy audio blob
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const buffer = audioContext.createBuffer(1, 44100, 44100); // 1 second of silence

            // Convert to blob (simplified)
            const dummyBlob = new Blob(['dummy audio data'], { type: 'audio/webm' });

            const formData = new FormData();
            formData.append('voice_file', dummyBlob, 'test_voice.webm');
            formData.append('conversation_id', conversationId);
            formData.append('duration', 1);

            showResult('Testing API with dummy data...', 'debug');

            try {
                const response = await fetch('api/send_voice_message.php', {
                    method: 'POST',
                    body: formData
                });

                const responseText = await response.text();
                showResult('Raw API Response:\n' + responseText, 'debug');

                try {
                    const result = JSON.parse(responseText);

                    if (result.success) {
                        showResult('✅ API test successful!', 'success');
                    } else {
                        showResult('❌ API test failed: ' + result.message, 'error');
                        if (result.debug) {
                            showResult('Debug Info:\n' + JSON.stringify(result.debug, null, 2), 'debug');
                        }
                    }
                } catch (parseError) {
                    showResult('❌ Failed to parse API response: ' + parseError.message, 'error');
                }

            } catch (error) {
                showResult('❌ API test network error: ' + error.message, 'error');
            }
        }

        // Check if user is logged in
        fetch('api/check_session.php')
            .then(response => response.json())
            .then(data => {
                if (!data.logged_in) {
                    showResult('⚠️ You need to be logged in to test voice messages. Please log in first.', 'error');
                    showResult('Please go to: <a href="pages/login.php">Login Page</a>', 'error');
                } else {
                    showResult('✅ Logged in as user ID: ' + data.user_id, 'success');
                }
            })
            .catch(error => {
                showResult('❌ Failed to check login status: ' + error.message, 'error');
            });

        // Test if MediaRecorder is supported
        if (typeof MediaRecorder === 'undefined') {
            showResult('❌ MediaRecorder not supported in this browser', 'error');
        } else {
            showResult('✅ MediaRecorder supported', 'success');
        }

        // Test microphone permissions
        navigator.mediaDevices.getUserMedia({ audio: true })
            .then(stream => {
                showResult('✅ Microphone access granted', 'success');
                stream.getTracks().forEach(track => track.stop());
            })
            .catch(error => {
                showResult('❌ Microphone access denied: ' + error.message, 'error');
            });
    </script>
</body>
</html>
