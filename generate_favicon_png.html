<!DOCTYPE html>
<html>
<head>
    <title>Generate MoodifyMe Favicon PNG</title>
</head>
<body>
    <h2>MoodifyMe Favicon Generator</h2>
    <canvas id="faviconCanvas" width="32" height="32" style="border: 1px solid #ccc; image-rendering: pixelated; width: 128px; height: 128px;"></canvas>
    <br><br>
    <button onclick="downloadFavicon()">Download as favicon.ico</button>
    <button onclick="downloadPNG()">Download as PNG</button>
    
    <script>
        const canvas = document.getElementById('faviconCanvas');
        const ctx = canvas.getContext('2d');
        
        function drawFavicon() {
            // Clear canvas
            ctx.clearRect(0, 0, 32, 32);
            
            // Create gradient
            const gradient = ctx.createLinearGradient(0, 0, 32, 32);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(0.5, '#764ba2');
            gradient.addColorStop(1, '#f093fb');
            
            // Draw background circle
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(16, 16, 15, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw border
            ctx.strokeStyle = 'rgba(255,255,255,0.3)';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.arc(16, 16, 15, 0, 2 * Math.PI);
            ctx.stroke();
            
            // Draw eyes
            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            ctx.arc(11, 12, 1.5, 0, 2 * Math.PI);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(21, 12, 1.5, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw smile
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 2;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.arc(16, 14, 6, 0.3, Math.PI - 0.3);
            ctx.stroke();
            
            // Add highlight
            ctx.fillStyle = 'rgba(255,255,255,0.2)';
            ctx.beginPath();
            ctx.ellipse(13, 10, 3, 2, 0, 0, 2 * Math.PI);
            ctx.fill();
        }
        
        function downloadFavicon() {
            const link = document.createElement('a');
            link.download = 'favicon.ico';
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        function downloadPNG() {
            const link = document.createElement('a');
            link.download = 'favicon-32x32.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // Draw the favicon
        drawFavicon();
        
        console.log('Favicon generated! Click download to save.');
    </script>
</body>
</html>
