<?php
/**
 * Send Voice Message API
 * Handles voice message upload and sending
 */

session_start();

require_once '../config.php';
require_once '../includes/db_connect.php';
require_once '../includes/notification_functions.php';

// Set JSON response header
header('Content-Type: application/json');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Function to log errors
function logError($message, $data = null) {
    $logMessage = date('Y-m-d H:i:s') . " - " . $message;
    if ($data) {
        $logMessage .= " - Data: " . json_encode($data);
    }
    error_log($logMessage);
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    logError("Voice message: User not authenticated");
    echo json_encode(['success' => false, 'message' => 'User not authenticated']);
    exit;
}

$currentUserId = $_SESSION['user_id'];

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    logError("Voice message: Invalid request method", $_SERVER['REQUEST_METHOD']);
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Get POST parameters
$conversationId = (int)($_POST['conversation_id'] ?? 0);
$duration = (float)($_POST['duration'] ?? 0);

logError("Voice message request", [
    'conversation_id' => $conversationId,
    'duration' => $duration,
    'files' => array_keys($_FILES),
    'post_data' => $_POST
]);

// Validate inputs
if ($conversationId <= 0) {
    logError("Voice message: Invalid conversation ID", $conversationId);
    echo json_encode(['success' => false, 'message' => 'Invalid conversation ID']);
    exit;
}

if ($duration <= 0) {
    logError("Voice message: Invalid duration", $duration);
    echo json_encode(['success' => false, 'message' => 'Invalid voice message duration']);
    exit;
}

// Check if voice file was uploaded
if (!isset($_FILES['voice_file'])) {
    logError("Voice message: No voice_file in FILES", $_FILES);
    echo json_encode(['success' => false, 'message' => 'No voice file uploaded']);
    exit;
}

if ($_FILES['voice_file']['error'] !== UPLOAD_ERR_OK) {
    logError("Voice message: Upload error", $_FILES['voice_file']['error']);
    echo json_encode(['success' => false, 'message' => 'Upload error: ' . $_FILES['voice_file']['error']]);
    exit;
}

$voiceFile = $_FILES['voice_file'];

// Validate file type
$allowedTypes = ['audio/webm', 'audio/wav', 'audio/mp3', 'audio/ogg'];
$fileType = $voiceFile['type'];

if (!in_array($fileType, $allowedTypes)) {
    echo json_encode(['success' => false, 'message' => 'Invalid file type. Only audio files are allowed.']);
    exit;
}

// Validate file size (max 10MB)
$maxSize = 10 * 1024 * 1024; // 10MB
if ($voiceFile['size'] > $maxSize) {
    echo json_encode(['success' => false, 'message' => 'File too large. Maximum size is 10MB.']);
    exit;
}

try {
    // Start transaction
    $conn->begin_transaction();

    logError("Voice message: Starting database operations");

    // Verify user is participant in conversation
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count
        FROM conversation_participants
        WHERE conversation_id = ? AND user_id = ?
    ");

    if (!$stmt) {
        logError("Voice message: Database prepare failed", $conn->error);
        throw new Exception("Database prepare failed: " . $conn->error);
    }

    $stmt->bind_param("ii", $conversationId, $currentUserId);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $stmt->close();

    logError("Voice message: Participant check", ['count' => $row['count']]);

    if ($row['count'] == 0) {
        logError("Voice message: User not participant", ['user_id' => $currentUserId, 'conversation_id' => $conversationId]);
        throw new Exception("User is not a participant in this conversation");
    }
    
    // Create upload directory if it doesn't exist
    $uploadDir = '../uploads/voice/' . date('Y/m');
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // Generate unique filename
    $fileExtension = pathinfo($voiceFile['name'], PATHINFO_EXTENSION);
    if (empty($fileExtension)) {
        // Determine extension from MIME type
        $mimeToExt = [
            'audio/webm' => 'webm',
            'audio/wav' => 'wav',
            'audio/mp3' => 'mp3',
            'audio/ogg' => 'ogg'
        ];
        $fileExtension = $mimeToExt[$fileType] ?? 'webm';
    }
    
    $fileName = 'voice_' . time() . '_' . $currentUserId . '_' . uniqid() . '.' . $fileExtension;
    $filePath = $uploadDir . '/' . $fileName;
    $relativeFilePath = 'uploads/voice/' . date('Y/m') . '/' . $fileName;
    
    // Move uploaded file
    if (!move_uploaded_file($voiceFile['tmp_name'], $filePath)) {
        throw new Exception("Failed to save voice file");
    }
    
    // Check if voice message columns exist
    $result = $conn->query("SHOW COLUMNS FROM messages LIKE 'message_content_type'");
    if ($result->num_rows == 0) {
        logError("Voice message: message_content_type column missing");
        throw new Exception("Database schema not updated. Please run voice message schema migration.");
    }

    logError("Voice message: Inserting message", [
        'sender_id' => $currentUserId,
        'conversation_id' => $conversationId,
        'file_path' => $relativeFilePath,
        'duration' => $duration,
        'file_size' => $voiceFile['size']
    ]);

    // Insert the message
    $stmt = $conn->prepare("
        INSERT INTO messages (sender_id, conversation_id, content, message_content_type, voice_file_path, voice_duration, voice_file_size, created_at)
        VALUES (?, ?, ?, 'voice', ?, ?, ?, NOW())
    ");

    if (!$stmt) {
        logError("Voice message: Message insert prepare failed", $conn->error);
        throw new Exception("Database prepare failed: " . $conn->error);
    }

    $content = "Voice message (" . round($duration, 1) . "s)";
    $stmt->bind_param("iissii", $currentUserId, $conversationId, $content, $relativeFilePath, $duration, $voiceFile['size']);

    if (!$stmt->execute()) {
        logError("Voice message: Message insert failed", $stmt->error);
        throw new Exception("Failed to insert message: " . $stmt->error);
    }

    $messageId = $conn->insert_id;
    $stmt->close();

    logError("Voice message: Message inserted", ['message_id' => $messageId]);
    
    // Insert voice message metadata
    $stmt = $conn->prepare("
        INSERT INTO voice_messages (message_id, original_filename, file_path, file_size, duration, format, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, NOW())
    ");
    
    if ($stmt) {
        $format = $fileExtension;
        $stmt->bind_param("issids", $messageId, $voiceFile['name'], $relativeFilePath, $voiceFile['size'], $duration, $format);
        $stmt->execute();
        $stmt->close();
    }
    
    // Update conversation's last message timestamp
    $stmt = $conn->prepare("UPDATE conversations SET last_message_at = NOW() WHERE id = ?");
    if ($stmt) {
        $stmt->bind_param("i", $conversationId);
        $stmt->execute();
        $stmt->close();
    }

    // Create notifications for other participants
    $stmt = $conn->prepare("
        SELECT cp.user_id 
        FROM conversation_participants cp 
        WHERE cp.conversation_id = ? AND cp.user_id != ?
    ");
    if ($stmt) {
        $stmt->bind_param("ii", $conversationId, $currentUserId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($participant = $result->fetch_assoc()) {
            createMessageNotification($participant['user_id'], $currentUserId, $content);
        }
        $stmt->close();
    }
    
    // Get the inserted message with user details
    $stmt = $conn->prepare("
        SELECT m.*, u.username, u.display_name, u.profile_picture as profile_image
        FROM messages m
        JOIN users u ON m.sender_id = u.id
        WHERE m.id = ?
    ");
    
    if ($stmt) {
        $stmt->bind_param("i", $messageId);
        $stmt->execute();
        $result = $stmt->get_result();
        $message = $result->fetch_assoc();
        $stmt->close();
        
        // Commit transaction
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Voice message sent successfully',
            'data' => $message
        ]);
    } else {
        // Commit transaction
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Voice message sent successfully',
            'message_id' => $messageId
        ]);
    }
    
} catch (Exception $e) {
    // Rollback transaction
    $conn->rollback();

    logError("Voice message: Exception caught", [
        'message' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);

    // Delete uploaded file if it exists
    if (isset($filePath) && file_exists($filePath)) {
        unlink($filePath);
        logError("Voice message: Cleaned up uploaded file", $filePath);
    }

    error_log("Send voice message error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Failed to send voice message: ' . $e->getMessage(),
        'error' => $e->getMessage(),
        'debug' => [
            'conversation_id' => $conversationId ?? 'not set',
            'duration' => $duration ?? 'not set',
            'user_id' => $currentUserId ?? 'not set',
            'file_info' => isset($voiceFile) ? [
                'name' => $voiceFile['name'],
                'size' => $voiceFile['size'],
                'type' => $voiceFile['type'],
                'error' => $voiceFile['error']
            ] : 'not set'
        ]
    ]);
}
?>
