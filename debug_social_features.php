<?php
/**
 * MoodifyMe - Social Features Debug <PERSON>ript
 * Run this script to diagnose social features issues
 */

require_once 'config.php';
require_once 'includes/db_connect.php';

echo "<h1>MoodifyMe Social Features Diagnostic</h1>";

// Check database connection
echo "<h2>1. Database Connection</h2>";
if ($conn->connect_error) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $conn->connect_error . "</p>";
    exit;
} else {
    echo "<p style='color: green;'>✅ Database connection successful</p>";
}

// Check if required tables exist
echo "<h2>2. Required Tables</h2>";
$requiredTables = ['users', 'user_connections', 'user_follows', 'notifications', 'user_online_status'];

foreach ($requiredTables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "<p style='color: green;'>✅ Table '$table' exists</p>";
    } else {
        echo "<p style='color: red;'>❌ Table '$table' missing</p>";
    }
}

// Check users table structure
echo "<h2>3. Users Table Structure</h2>";
$result = $conn->query("DESCRIBE users");
$columns = [];
while ($row = $result->fetch_assoc()) {
    $columns[] = $row['Field'];
}

$requiredColumns = ['connection_count', 'follower_count', 'following_count', 'display_name'];
foreach ($requiredColumns as $column) {
    if (in_array($column, $columns)) {
        echo "<p style='color: green;'>✅ Column '$column' exists</p>";
    } else {
        echo "<p style='color: red;'>❌ Column '$column' missing</p>";
    }
}

// Check user_connections table
echo "<h2>4. User Connections Data</h2>";
$result = $conn->query("SELECT COUNT(*) as total FROM user_connections");
$row = $result->fetch_assoc();
echo "<p>Total connections: " . $row['total'] . "</p>";

$result = $conn->query("SELECT status, COUNT(*) as count FROM user_connections GROUP BY status");
echo "<p>Connection status breakdown:</p><ul>";
while ($row = $result->fetch_assoc()) {
    echo "<li>" . $row['status'] . ": " . $row['count'] . "</li>";
}
echo "</ul>";

// Check for pending requests
$result = $conn->query("
    SELECT uc.id, u1.username as requester, u2.username as receiver, uc.created_at
    FROM user_connections uc
    JOIN users u1 ON uc.requester_id = u1.id
    JOIN users u2 ON uc.receiver_id = u2.id
    WHERE uc.status = 'pending'
    ORDER BY uc.created_at DESC
    LIMIT 10
");

echo "<h2>5. Recent Pending Requests</h2>";
if ($result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Requester</th><th>Receiver</th><th>Date</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['requester'] . "</td>";
        echo "<td>" . $row['receiver'] . "</td>";
        echo "<td>" . $row['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No pending connection requests found.</p>";
}

// Check PHP error log
echo "<h2>6. Recent PHP Errors</h2>";
$errorLog = ini_get('error_log');
if ($errorLog && file_exists($errorLog)) {
    $errors = file_get_contents($errorLog);
    $recentErrors = array_slice(explode("\n", $errors), -20);
    echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 300px; overflow-y: scroll;'>";
    echo implode("\n", $recentErrors);
    echo "</pre>";
} else {
    echo "<p>Error log not accessible or not configured.</p>";
}

echo "<h2>7. Recommendations</h2>";
echo "<ol>";
echo "<li>If any tables or columns are missing, run the migration script: <code>database/fix_social_features.sql</code></li>";
echo "<li>Check your PHP error logs for specific error messages</li>";
echo "<li>Ensure your database user has proper permissions</li>";
echo "<li>Test the connection acceptance with error logging enabled</li>";
echo "</ol>";

echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Run the database migration script if needed</li>";
echo "<li>Try accepting a friend request again</li>";
echo "<li>Check the browser console and network tab for detailed error messages</li>";
echo "</ol>";
?>
