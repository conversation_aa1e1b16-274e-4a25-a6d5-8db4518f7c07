<?php
/**
 * Test Follow Functionality
 */

require_once 'config.php';
require_once 'includes/db_connect.php';
require_once 'includes/social_functions.php';

session_start();

echo "<h1>Follow Functionality Test</h1>";

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "<p style='color: red;'>❌ No user logged in. Please log in first.</p>";
    exit;
}

$currentUserId = $_SESSION['user_id'];
echo "<p><strong>Current User ID:</strong> $currentUserId</p>";

// Get current user info
$stmt = $conn->prepare("SELECT username, email FROM users WHERE id = ?");
$stmt->bind_param("i", $currentUserId);
$stmt->execute();
$user = $stmt->get_result()->fetch_assoc();
echo "<p><strong>Current User:</strong> " . $user['username'] . " (" . $user['email'] . ")</p>";

// Check if user_follows table exists
echo "<h2>1. Database Check</h2>";
$tableCheck = $conn->query("SHOW TABLES LIKE 'user_follows'");
if ($tableCheck->num_rows > 0) {
    echo "<p style='color: green;'>✅ user_follows table exists</p>";
} else {
    echo "<p style='color: red;'>❌ user_follows table missing</p>";
    echo "<p><strong>Solution:</strong> Run the database migration script: <code>database/fix_social_features.sql</code></p>";
}

// Check columns in users table
$columnCheck = $conn->query("SHOW COLUMNS FROM users LIKE 'follower_count'");
if ($columnCheck->num_rows > 0) {
    echo "<p style='color: green;'>✅ follower_count column exists</p>";
} else {
    echo "<p style='color: red;'>❌ follower_count column missing</p>";
}

$columnCheck = $conn->query("SHOW COLUMNS FROM users LIKE 'following_count'");
if ($columnCheck->num_rows > 0) {
    echo "<p style='color: green;'>✅ following_count column exists</p>";
} else {
    echo "<p style='color: red;'>❌ following_count column missing</p>";
}

// Show current follow relationships
echo "<h2>2. Current Follow Relationships</h2>";
if ($tableCheck->num_rows > 0) {
    $result = $conn->query("
        SELECT uf.*, 
               u1.username as follower_name, 
               u2.username as following_name
        FROM user_follows uf
        JOIN users u1 ON uf.follower_id = u1.id
        JOIN users u2 ON uf.following_id = u2.id
        ORDER BY uf.created_at DESC
        LIMIT 10
    ");

    if ($result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Follower</th><th>Following</th><th>Date</th></tr>";
        while ($row = $result->fetch_assoc()) {
            $style = '';
            if ($row['follower_id'] == $currentUserId || $row['following_id'] == $currentUserId) {
                $style = 'background-color: #ffffcc;'; // Highlight current user's follows
            }
            echo "<tr style='$style'>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['follower_name'] . " (ID: " . $row['follower_id'] . ")</td>";
            echo "<td>" . $row['following_name'] . " (ID: " . $row['following_id'] . ")</td>";
            echo "<td>" . $row['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No follow relationships found.</p>";
    }
} else {
    echo "<p style='color: red;'>Cannot check follow relationships - table missing</p>";
}

// Show users available to follow
echo "<h2>3. Test Follow Functions</h2>";
$result = $conn->query("SELECT id, username FROM users WHERE id != $currentUserId LIMIT 5");
if ($result->num_rows > 0) {
    echo "<p>Click these buttons to test the follow functionality:</p>";
    while ($row = $result->fetch_assoc()) {
        $isFollowing = false;
        if ($tableCheck->num_rows > 0) {
            $followCheck = $conn->prepare("SELECT id FROM user_follows WHERE follower_id = ? AND following_id = ?");
            $followCheck->bind_param("ii", $currentUserId, $row['id']);
            $followCheck->execute();
            $isFollowing = $followCheck->get_result()->num_rows > 0;
        }
        
        $buttonText = $isFollowing ? 'Unfollow' : 'Follow';
        $action = $isFollowing ? 'unfollow' : 'follow';
        $buttonClass = $isFollowing ? 'btn-outline-primary' : 'btn-primary';
        
        echo "<button class='btn $buttonClass' onclick=\"testFollow(" . $row['id'] . ", '$action', this)\">";
        echo "$buttonText " . $row['username'] . " (ID: " . $row['id'] . ")";
        echo "</button> ";
    }
} else {
    echo "<p>No other users found to test with.</p>";
}

// Show recent errors
echo "<h2>4. Recent PHP Errors</h2>";
$errorLogFile = __DIR__ . '/php_errors.log';
if (file_exists($errorLogFile)) {
    $lines = file($errorLogFile);
    $recentLines = array_slice($lines, -10);
    echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 200px; overflow-y: scroll;'>";
    echo htmlspecialchars(implode('', $recentLines));
    echo "</pre>";
} else {
    echo "<p>No error log file found. Errors may be logged elsewhere.</p>";
}

?>

<script>
function testFollow(userId, action, button) {
    console.log(`Testing ${action} for user:`, userId);
    
    button.disabled = true;
    button.textContent = 'Processing...';
    
    fetch('<?php echo APP_URL; ?>/api/social_actions.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=${action}&user_id=${userId}`
    })
    .then(response => response.json())
    .then(data => {
        console.log('Response:', data);
        alert('Response: ' + JSON.stringify(data, null, 2));
        if (data.success) {
            location.reload();
        } else {
            button.disabled = false;
            // Restore original button text
            const username = button.textContent.split(' ').slice(1).join(' ');
            button.textContent = (action === 'follow' ? 'Follow ' : 'Unfollow ') + username;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error: ' + error.message);
        button.disabled = false;
        // Restore original button text
        const username = button.textContent.split(' ').slice(1).join(' ');
        button.textContent = (action === 'follow' ? 'Follow ' : 'Unfollow ') + username;
    });
}
</script>

<style>
.btn {
    margin: 5px;
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}
.btn-primary {
    background: #007bff;
    color: white;
}
.btn-outline-primary {
    background: white;
    color: #007bff;
    border: 1px solid #007bff;
}
.btn:hover {
    opacity: 0.8;
}
.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
table {
    margin: 10px 0;
}
th, td {
    padding: 8px;
    text-align: left;
}
th {
    background: #f8f9fa;
}
</style>
