<?php
/**
 * Debug Unfollow Issue
 */

require_once 'config.php';
require_once 'includes/db_connect.php';
require_once 'includes/social_functions.php';

session_start();

echo "<h1>Unfollow Debug Test</h1>";

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "<p style='color: red;'>❌ No user logged in. Please log in first.</p>";
    exit;
}

$currentUserId = $_SESSION['user_id'];
echo "<p><strong>Current User ID:</strong> $currentUserId</p>";

// Get current user info
$stmt = $conn->prepare("SELECT username, email FROM users WHERE id = ?");
$stmt->bind_param("i", $currentUserId);
$stmt->execute();
$user = $stmt->get_result()->fetch_assoc();
echo "<p><strong>Current User:</strong> " . $user['username'] . " (" . $user['email'] . ")</p>";

// Check if user_follows table exists
echo "<h2>1. Database Check</h2>";
$tableCheck = $conn->query("SHOW TABLES LIKE 'user_follows'");
if ($tableCheck->num_rows > 0) {
    echo "<p style='color: green;'>✅ user_follows table exists</p>";
} else {
    echo "<p style='color: red;'>❌ user_follows table missing</p>";
    exit;
}

// Show current follows by this user
echo "<h2>2. Current Follows by You</h2>";
$result = $conn->query("
    SELECT uf.*, u.username as following_name
    FROM user_follows uf
    JOIN users u ON uf.following_id = u.id
    WHERE uf.follower_id = $currentUserId
    ORDER BY uf.created_at DESC
");

if ($result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Following User</th><th>Following ID</th><th>Date</th><th>Action</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['following_name'] . "</td>";
        echo "<td>" . $row['following_id'] . "</td>";
        echo "<td>" . $row['created_at'] . "</td>";
        echo "<td><button onclick=\"testUnfollow(" . $row['following_id'] . ", this)\">Test Unfollow</button></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>You are not following anyone yet.</p>";
    
    // Show other users to follow first
    echo "<h3>Users you can follow:</h3>";
    $otherUsers = $conn->query("SELECT id, username FROM users WHERE id != $currentUserId LIMIT 3");
    while ($otherUser = $otherUsers->fetch_assoc()) {
        echo "<button onclick=\"testFollow(" . $otherUser['id'] . ", this)\">";
        echo "Follow " . $otherUser['username'] . " (ID: " . $otherUser['id'] . ")";
        echo "</button> ";
    }
}

// Manual test section
echo "<h2>3. Manual Test</h2>";
echo "<p>Enter a user ID you want to test unfollowing:</p>";
echo "<input type='number' id='testUserId' placeholder='User ID'>";
echo "<button onclick='manualUnfollowTest()'>Test Unfollow</button>";

// Show recent errors
echo "<h2>4. Recent PHP Errors</h2>";
$errorLogFile = __DIR__ . '/php_errors.log';
if (file_exists($errorLogFile)) {
    $lines = file($errorLogFile);
    $recentLines = array_slice($lines, -15);
    echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 300px; overflow-y: scroll;'>";
    echo htmlspecialchars(implode('', $recentLines));
    echo "</pre>";
} else {
    echo "<p>No error log file found. Check your PHP configuration.</p>";
}

?>

<script>
function testUnfollow(userId, button) {
    console.log('Testing unfollow for user:', userId);
    
    button.disabled = true;
    button.textContent = 'Processing...';
    
    fetch('<?php echo APP_URL; ?>/api/social_actions.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=unfollow&user_id=${userId}`
    })
    .then(response => response.json())
    .then(data => {
        console.log('Unfollow response:', data);
        alert('Unfollow Response:\n' + JSON.stringify(data, null, 2));
        if (data.success) {
            location.reload();
        } else {
            button.disabled = false;
            button.textContent = 'Test Unfollow';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error: ' + error.message);
        button.disabled = false;
        button.textContent = 'Test Unfollow';
    });
}

function testFollow(userId, button) {
    console.log('Testing follow for user:', userId);
    
    button.disabled = true;
    button.textContent = 'Processing...';
    
    fetch('<?php echo APP_URL; ?>/api/social_actions.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=follow&user_id=${userId}`
    })
    .then(response => response.json())
    .then(data => {
        console.log('Follow response:', data);
        alert('Follow Response:\n' + JSON.stringify(data, null, 2));
        if (data.success) {
            location.reload();
        } else {
            button.disabled = false;
            button.textContent = button.textContent.replace('Processing...', 'Follow');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error: ' + error.message);
        button.disabled = false;
        button.textContent = button.textContent.replace('Processing...', 'Follow');
    });
}

function manualUnfollowTest() {
    const userId = document.getElementById('testUserId').value;
    if (!userId) {
        alert('Please enter a user ID');
        return;
    }
    
    console.log('Manual unfollow test for user:', userId);
    
    fetch('<?php echo APP_URL; ?>/api/social_actions.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=unfollow&user_id=${userId}`
    })
    .then(response => response.json())
    .then(data => {
        console.log('Manual unfollow response:', data);
        alert('Manual Unfollow Response:\n' + JSON.stringify(data, null, 2));
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error: ' + error.message);
    });
}
</script>

<style>
table {
    margin: 10px 0;
}
th, td {
    padding: 8px;
    text-align: left;
}
th {
    background: #f8f9fa;
}
button {
    margin: 5px;
    padding: 5px 10px;
    cursor: pointer;
}
input {
    margin: 5px;
    padding: 5px;
}
</style>
