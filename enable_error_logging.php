<?php
/**
 * Enable error logging for debugging
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Enable error logging
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/php_errors.log');

echo "<h1>Error Logging Enabled</h1>";
echo "<p>PHP errors will now be logged to: " . __DIR__ . '/php_errors.log' . "</p>";

// Test error logging
error_log("TEST: Error logging is working - " . date('Y-m-d H:i:s'));

echo "<p>✅ Test error logged successfully</p>";
echo "<p><strong>Next steps:</strong></p>";
echo "<ol>";
echo "<li>Try accepting a friend request again</li>";
echo "<li>Check the error log file: <code>php_errors.log</code></li>";
echo "<li>Or run the connection debug script: <a href='check_connections.php'>check_connections.php</a></li>";
echo "</ol>";

// Show current error log if it exists
$errorLogFile = __DIR__ . '/php_errors.log';
if (file_exists($errorLogFile)) {
    echo "<h2>Current Error Log (last 20 lines)</h2>";
    $lines = file($errorLogFile);
    $recentLines = array_slice($lines, -20);
    echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 300px; overflow-y: scroll;'>";
    echo htmlspecialchars(implode('', $recentLines));
    echo "</pre>";
}
?>
