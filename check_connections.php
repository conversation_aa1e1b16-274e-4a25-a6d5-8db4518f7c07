<?php
/**
 * Check current user connections and debug the issue
 */

require_once 'config.php';
require_once 'includes/db_connect.php';

session_start();

echo "<h1>Connection Debug for Current User</h1>";

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "<p style='color: red;'>❌ No user logged in. Please log in first.</p>";
    exit;
}

$currentUserId = $_SESSION['user_id'];
echo "<p><strong>Current User ID:</strong> $currentUserId</p>";

// Get current user info
$stmt = $conn->prepare("SELECT username, email FROM users WHERE id = ?");
$stmt->bind_param("i", $currentUserId);
$stmt->execute();
$user = $stmt->get_result()->fetch_assoc();
echo "<p><strong>Current User:</strong> " . $user['username'] . " (" . $user['email'] . ")</p>";

echo "<h2>All User Connections</h2>";
$result = $conn->query("
    SELECT uc.*, 
           u1.username as requester_name, 
           u2.username as receiver_name
    FROM user_connections uc
    JOIN users u1 ON uc.requester_id = u1.id
    JOIN users u2 ON uc.receiver_id = u2.id
    ORDER BY uc.created_at DESC
");

if ($result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Requester</th><th>Receiver</th><th>Status</th><th>Created</th><th>Updated</th></tr>";
    while ($row = $result->fetch_assoc()) {
        $style = '';
        if ($row['requester_id'] == $currentUserId || $row['receiver_id'] == $currentUserId) {
            $style = 'background-color: #ffffcc;'; // Highlight current user's connections
        }
        echo "<tr style='$style'>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['requester_name'] . " (ID: " . $row['requester_id'] . ")</td>";
        echo "<td>" . $row['receiver_name'] . " (ID: " . $row['receiver_id'] . ")</td>";
        echo "<td><strong>" . $row['status'] . "</strong></td>";
        echo "<td>" . $row['created_at'] . "</td>";
        echo "<td>" . $row['updated_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No connections found in database.</p>";
}

echo "<h2>Pending Requests for Current User</h2>";
$stmt = $conn->prepare("
    SELECT uc.*, u.username as requester_name
    FROM user_connections uc
    JOIN users u ON uc.requester_id = u.id
    WHERE uc.receiver_id = ? AND uc.status = 'pending'
    ORDER BY uc.created_at DESC
");
$stmt->bind_param("i", $currentUserId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>From User</th><th>Date</th><th>Action</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['requester_name'] . " (ID: " . $row['requester_id'] . ")</td>";
        echo "<td>" . $row['created_at'] . "</td>";
        echo "<td>";
        echo "<button onclick=\"testAccept(" . $row['requester_id'] . ")\">Test Accept</button> ";
        echo "<button onclick=\"testDecline(" . $row['requester_id'] . ")\">Test Decline</button>";
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>⚠️ No pending requests found for current user.</p>";
    echo "<p>This might explain why the accept button isn't working - there may be no actual pending requests in the database.</p>";
}

echo "<h2>Test Connection Functions</h2>";
echo "<p>Use these buttons to test the connection functions directly:</p>";

// Get all users for testing
$result = $conn->query("SELECT id, username FROM users WHERE id != $currentUserId LIMIT 5");
echo "<h3>Send Test Connection Request To:</h3>";
while ($row = $result->fetch_assoc()) {
    echo "<button onclick=\"testConnect(" . $row['id'] . ")\">" . $row['username'] . " (ID: " . $row['id'] . ")</button> ";
}

?>

<script>
function testAccept(userId) {
    console.log('Testing accept for user:', userId);
    
    fetch('<?php echo APP_URL; ?>/api/social_actions.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=accept_connection&user_id=${userId}`
    })
    .then(response => response.json())
    .then(data => {
        console.log('Response:', data);
        alert('Response: ' + JSON.stringify(data));
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error: ' + error.message);
    });
}

function testDecline(userId) {
    console.log('Testing decline for user:', userId);
    
    fetch('<?php echo APP_URL; ?>/api/social_actions.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=decline_connection&user_id=${userId}`
    })
    .then(response => response.json())
    .then(data => {
        console.log('Response:', data);
        alert('Response: ' + JSON.stringify(data));
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error: ' + error.message);
    });
}

function testConnect(userId) {
    console.log('Testing connect to user:', userId);
    
    fetch('<?php echo APP_URL; ?>/api/social_actions.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=connect&user_id=${userId}`
    })
    .then(response => response.json())
    .then(data => {
        console.log('Response:', data);
        alert('Response: ' + JSON.stringify(data));
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error: ' + error.message);
    });
}
</script>

<style>
button {
    margin: 5px;
    padding: 8px 12px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}
button:hover {
    background: #0056b3;
}
table {
    margin: 10px 0;
}
th, td {
    padding: 8px;
    text-align: left;
}
th {
    background: #f8f9fa;
}
</style>
