<?php
/**
 * Demo page to show real-time functionality
 */

require_once 'config.php';
require_once 'includes/db_connect.php';

session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: " . APP_URL . "/pages/login.php");
    exit;
}

$currentUserId = $_SESSION['user_id'];

// Get current user info
$stmt = $conn->prepare("SELECT username, email FROM users WHERE id = ?");
$stmt->bind_param("i", $currentUserId);
$stmt->execute();
$user = $stmt->get_result()->fetch_assoc();

include 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-bolt"></i> Real-Time Social Features Demo</h4>
                    <p class="mb-0 text-muted">No more page refreshes! See instant updates when you interact with social features.</p>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5><i class="fas fa-check-circle text-success"></i> What's New</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <i class="fas fa-user-plus text-primary"></i>
                                    <strong>Real-time Follow/Unfollow</strong><br>
                                    <small class="text-muted">Follow buttons update instantly without page reload</small>
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-handshake text-success"></i>
                                    <strong>Live Connection Requests</strong><br>
                                    <small class="text-muted">Accept/decline requests with smooth animations</small>
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-bell text-warning"></i>
                                    <strong>Toast Notifications</strong><br>
                                    <small class="text-muted">Get instant feedback for all actions</small>
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-spinner text-info"></i>
                                    <strong>Loading States</strong><br>
                                    <small class="text-muted">Visual feedback while processing requests</small>
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-chart-line text-primary"></i>
                                    <strong>Live Counter Updates</strong><br>
                                    <small class="text-muted">Follower/connection counts update automatically</small>
                                </li>
                            </ul>
                        </div>
                        
                        <div class="col-md-6">
                            <h5><i class="fas fa-play-circle text-primary"></i> Try It Out</h5>
                            <div class="d-grid gap-2">
                                <a href="<?php echo APP_URL; ?>/pages/connections.php" class="btn btn-primary">
                                    <i class="fas fa-users"></i> Test Connection Requests
                                </a>
                                <a href="<?php echo APP_URL; ?>/pages/user_directory.php" class="btn btn-outline-primary">
                                    <i class="fas fa-search"></i> Test Follow/Unfollow
                                </a>
                                <a href="<?php echo APP_URL; ?>/pages/social_profile.php?id=<?php echo $currentUserId; ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-user"></i> View Your Profile
                                </a>
                            </div>
                            
                            <div class="mt-4">
                                <h6><i class="fas fa-info-circle"></i> How It Works</h6>
                                <ol class="small">
                                    <li>Click any social action button (follow, accept, etc.)</li>
                                    <li>See the loading spinner animation</li>
                                    <li>Watch the UI update in real-time</li>
                                    <li>Get a toast notification confirming the action</li>
                                    <li>No page refresh needed!</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <h5><i class="fas fa-code"></i> Technical Improvements</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <i class="fas fa-tachometer-alt fa-2x text-success mb-2"></i>
                                            <h6>Better Performance</h6>
                                            <small>No full page reloads = faster interactions</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <i class="fas fa-heart fa-2x text-danger mb-2"></i>
                                            <h6>Better UX</h6>
                                            <small>Smooth animations and instant feedback</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <i class="fas fa-mobile-alt fa-2x text-primary mb-2"></i>
                                            <h6>Mobile Friendly</h6>
                                            <small>Works great on all devices</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Demo toast notification -->
<div class="mt-4 text-center">
    <button class="btn btn-success" onclick="showDemoToast()">
        <i class="fas fa-bell"></i> Test Toast Notification
    </button>
</div>

<script>
function showDemoToast() {
    const toast = document.createElement('div');
    toast.innerHTML = `
        <div class="alert alert-success alert-dismissible fade show" role="alert" 
             style="position: fixed; top: 20px; right: 20px; z-index: 1050; min-width: 300px; animation: slideInRight 0.3s ease;">
            <i class="fas fa-check-circle"></i> This is how real-time notifications work!
            <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    // Auto-remove after 4 seconds
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 4000);
}
</script>

<style>
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
